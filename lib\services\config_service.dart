import 'package:yaml/yaml.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import '../models/vaultwarden_config.dart';

/// Service class for managing docker-compose.yaml configuration
class ConfigService {
  final String dockerComposePath;

  ConfigService({required this.dockerComposePath});

  /// Create a default docker-compose.yaml file from assets if it doesn't exist
  Future<void> ensureDockerComposeExists() async {
    final file = File(dockerComposePath);
    if (!await file.exists()) {
      try {
        final assetContent = await rootBundle.loadString('assets/vaultwarden/docker-compose.yaml');
        await file.parent.create(recursive: true);
        await file.writeAsString(assetContent);
      } catch (e) {
        throw Exception('Failed to create docker-compose.yaml from assets: $e');
      }
    }
  }

  /// Load configuration from docker-compose.yaml
  Future<VaultwardenConfig> loadConfig() async {
    try {
      final file = File(dockerComposePath);
      if (!await file.exists()) {
        throw Exception('docker-compose.yaml not found at $dockerComposePath');
      }

      final content = await file.readAsString();
      final yamlDoc = loadYaml(content);
      
      final services = yamlDoc['services'];
      if (services == null || services['vaultwarden'] == null) {
        throw Exception('Invalid docker-compose.yaml: vaultwarden service not found');
      }

      final vaultwardenService = services['vaultwarden'];
      
      // Extract signups setting
      bool signupsAllowed = false;
      final environment = vaultwardenService['environment'];
      if (environment != null && environment['SIGNUPS_ALLOWED'] != null) {
        signupsAllowed = environment['SIGNUPS_ALLOWED'].toString().toLowerCase() == 'true';
      }
      
      // Extract port setting
      int portNumber = 11001;
      bool localhostOnly = true;
      final ports = vaultwardenService['ports'];
      if (ports != null && ports.isNotEmpty) {
        final portMapping = ports[0].toString();
        
        // Handle different port mapping formats:
        // - "11001:80" (old format - accessible from LAN)
        // - "127.0.0.1:11001:80" (new format - localhost only)
        if (portMapping.startsWith('127.0.0.1:')) {
          // Format: "127.0.0.1:11001:80"
          localhostOnly = true;
          final parts = portMapping.split(':');
          if (parts.length >= 3) {
            portNumber = int.tryParse(parts[1]) ?? 11001;
          }
        } else {
          // Format: "11001:80"
          localhostOnly = false;
          final portParts = portMapping.split(':');
          if (portParts.isNotEmpty) {
            portNumber = int.tryParse(portParts[0]) ?? 11001;
          }
        }
      }
      
      // Extract data path
      String dataPath = '';
      final volumes = vaultwardenService['volumes'];
      if (volumes != null && volumes.isNotEmpty) {
        final volumeMapping = volumes[0].toString();
        
        // Smart parsing to handle Windows paths with drive letters
        String hostPath = '';
        final lastColonIndex = volumeMapping.lastIndexOf(':');
        if (lastColonIndex > 1) { // Ensure it's not the drive letter colon
          hostPath = volumeMapping.substring(0, lastColonIndex);
        } else {
          // Fallback: assume the entire mapping is the host path
          hostPath = volumeMapping;
        }
        
        // Check if the path is already absolute
        if (path.isAbsolute(hostPath)) {
          dataPath = _normalizePath(hostPath);
        } else {
          // Only resolve relative paths against the docker-compose directory
          final dockerComposeDir = path.dirname(dockerComposePath);
          dataPath = _getCleanAbsolutePath(hostPath, dockerComposeDir);
        }
      }

      // Extract domain if present
      String domain = '';
      if (environment != null && environment['DOMAIN'] != null) {
        domain = environment['DOMAIN'].toString();
      }

      return VaultwardenConfig(
        signupsAllowed: signupsAllowed,
        dataPath: dataPath,
        port: portNumber,
        domain: domain,
        localhostOnly: localhostOnly,
      );
    } catch (e) {
      throw Exception('Failed to load configuration: $e');
    }
  }

  /// Save configuration to docker-compose.yaml
  Future<void> saveConfig(VaultwardenConfig config) async {
    try {
      final dockerComposeDir = path.dirname(dockerComposePath);
      
      // Determine the best way to represent the path in docker-compose
      String pathForDockerCompose;
      
      // Normalize the data path first
      final normalizedDataPath = _normalizePath(config.dataPath);
      final normalizedDockerComposeDir = _normalizePath(dockerComposeDir);
      
      // If the data path is within the project directory, use relative path
      if (path.isWithin(normalizedDockerComposeDir, normalizedDataPath)) {
        pathForDockerCompose = path.relative(normalizedDataPath, from: normalizedDockerComposeDir);
        // Ensure we use forward slashes for Docker compatibility
        pathForDockerCompose = pathForDockerCompose.replaceAll('\\', '/');
      } else {
        // For paths outside the project, use absolute path
        pathForDockerCompose = normalizedDataPath;
        // Convert to forward slashes for Docker compatibility
        pathForDockerCompose = pathForDockerCompose.replaceAll('\\', '/');
      }
      
      // Generate the docker-compose.yaml content
      final content = _generateDockerComposeContent(
        signupsAllowed: config.signupsAllowed,
        dataPath: pathForDockerCompose,
        port: config.port,
        localhostOnly: config.localhostOnly,
        domain: config.domain,
      );

      final file = File(dockerComposePath);
      await file.writeAsString(content);
    } catch (e) {
      throw Exception('Failed to save configuration: $e');
    }
  }

  /// Generate docker-compose.yaml content string
  String _generateDockerComposeContent({
    required bool signupsAllowed,
    required String dataPath,
    required int port,
    required bool localhostOnly,
    String domain = '',
  }) {
    final buffer = StringBuffer();
    buffer.writeln('services:');
    buffer.writeln('  vaultwarden:');
    buffer.writeln('    image: vaultwarden/server:latest');
    buffer.writeln('    container_name: vaultwarden');
    buffer.writeln('    restart: always');
    buffer.writeln('    environment:');
    
    if (domain.isNotEmpty) {
      buffer.writeln('      DOMAIN: "$domain"  # required when using a reverse proxy; your domain; vaultwarden needs to know it\'s https to work properly with attachments');
    } else {
      buffer.writeln('      # DOMAIN: "https://vaultwarden.example.com"  # required when using a reverse proxy; your domain; vaultwarden needs to know it\'s https to work properly with attachments');
    }
    
    buffer.writeln('      SIGNUPS_ALLOWED: "${signupsAllowed.toString()}" # Deactivate this with "false" after you have created your account so that no strangers can register');
    buffer.writeln('    volumes:');
    buffer.writeln('      - $dataPath:/data # the path before the : can be changed');
    buffer.writeln('    ports:');
    
    if (localhostOnly) {
      buffer.writeln('      - "127.0.0.1:$port:80" # you can replace the port with your preferred port');
    } else {
      buffer.writeln('      - "$port:80" # you can replace the port with your preferred port');
    }
    
    return buffer.toString();
  }

  /// Validate configuration values
  bool isValidConfig(VaultwardenConfig config) {
    if (config.port < 1 || config.port > 65535) return false;
    if (config.dataPath.isEmpty) return false;
    return true;
  }

  /// Normalize path for Windows compatibility
  String _normalizePath(String inputPath) {
    // Convert forward slashes to backslashes on Windows
    String normalized = path.normalize(inputPath);
    
    // Remove any redundant path separators
    while (normalized.contains('${path.separator}${path.separator}')) {
      normalized = normalized.replaceAll('${path.separator}${path.separator}', path.separator);
    }
    
    return normalized;
  }

  /// Get a clean absolute path from any input
  String _getCleanAbsolutePath(String inputPath, String basePath) {
    if (path.isAbsolute(inputPath)) {
      return _normalizePath(inputPath);
    } else {
      return _normalizePath(path.join(basePath, inputPath));
    }
  }
}
