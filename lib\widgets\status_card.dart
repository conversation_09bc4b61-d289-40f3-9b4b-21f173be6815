import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/vaultwarden_provider.dart';

class StatusCard extends StatelessWidget {
  const StatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<VaultwardenProvider>(
      builder: (context, provider, child) {
        return Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Icon(
                  provider.isRunning ? Icons.check_circle : Icons.stop_circle,
                  size: 48,
                  color: provider.isRunning ? Colors.green : Colors.red,
                ),
                const SizedBox(height: 12),
                Text(
                  provider.isRunning ? 'Vaultwarden is Running' : 'Vaultwarden is Stopped',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: provider.isRunning ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                if (provider.isRunning) ...[
                  SelectableText(
                    'Access at: ${provider.webInterfaceUrl}',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.blue,
                      decoration: TextDecoration.underline,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Port: ${provider.config.port}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
                if (provider.statusMessage.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    provider.statusMessage,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
                if (provider.isLoading) ...[
                  const SizedBox(height: 12),
                  const LinearProgressIndicator(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
