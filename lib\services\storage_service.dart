import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import '../models/vaultwarden_config.dart';

/// Service for persisting app configuration and state
class StorageService {
  static const String _configKey = 'vaultwarden_config';
  static const String _dockerComposePathKey = 'docker_compose_path';
  static const String _firstRunKey = 'first_run';

  /// Save configuration to persistent storage
  static Future<void> saveConfig(VaultwardenConfig config) async {
    final prefs = await SharedPreferences.getInstance();
    final configJson = jsonEncode(config.toMap());
    await prefs.setString(_configKey, configJson);
  }

  /// Load configuration from persistent storage
  static Future<VaultwardenConfig?> loadConfig() async {
    final prefs = await SharedPreferences.getInstance();
    final configJson = prefs.getString(_configKey);
    
    if (configJson != null) {
      try {
        final configMap = jsonDecode(configJson) as Map<String, dynamic>;
        return VaultwardenConfig.fromMap(configMap);
      } catch (e) {
        // If there's an error parsing the config, return null to use defaults
        return null;
      }
    }
    
    return null;
  }

  /// Save the docker-compose file path
  static Future<void> saveDockerComposePath(String path) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_dockerComposePathKey, path);
  }

  /// Load the docker-compose file path
  static Future<String?> loadDockerComposePath() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_dockerComposePathKey);
  }

  /// Check if this is the first run of the app
  static Future<bool> isFirstRun() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_firstRunKey) ?? true;
  }

  /// Mark that the app has been run before
  static Future<void> setFirstRunComplete() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_firstRunKey, false);
  }

  /// Clear all stored data (useful for reset functionality)
  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  /// Get app data directory for storing persistent files
  static Future<String> getAppDataDirectory() async {
    // For Windows, use the user's AppData directory
    // For other platforms, use appropriate directories
    final prefs = await SharedPreferences.getInstance();
    
    // Try to get a previously stored path first
    String? storedPath = prefs.getString('app_data_directory');
    if (storedPath != null) {
      return storedPath;
    }
    
    // Create a new app data directory
    String appDataPath;
    if (Platform.isWindows) {
      final appData = Platform.environment['APPDATA'];
      if (appData != null) {
        appDataPath = '$appData\\VaAulLT';
      } else {
        // Fallback to user profile
        final userProfile = Platform.environment['USERPROFILE'];
        appDataPath = '$userProfile\\AppData\\Roaming\\VaAulLT';
      }
    } else if (Platform.isLinux) {
      final home = Platform.environment['HOME'];
      appDataPath = '$home/.config/VaAulLT';
    } else if (Platform.isMacOS) {
      final home = Platform.environment['HOME'];
      appDataPath = '$home/Library/Application Support/VaAulLT';
    } else {
      // Fallback for other platforms
      appDataPath = './VaAulLT_data';
    }
    
    // Store the path for future use
    await prefs.setString('app_data_directory', appDataPath);
    
    return appDataPath;
  }
}
