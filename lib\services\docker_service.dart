import 'package:process_run/shell.dart';

/// Service class for Docker operations
class DockerService {
  final String dockerComposePath;
  final Shell _shell;

  DockerService({required this.dockerComposePath}) : _shell = Shell();

  /// Check if the Vaultwarden container is running
  Future<bool> isContainerRunning() async {
    try {
      // First try to check by container name directly (more reliable)
      final nameResult = await _shell.run('docker ps --filter "name=vaultwarden" --format "{{.Names}}"');

      if (nameResult.isNotEmpty && nameResult.first.stdout.toString().trim().isNotEmpty) {
        final containerNames = nameResult.first.stdout.toString().trim().split('\n');
        // Check if any running container has the exact name "vaultwarden"
        if (containerNames.any((name) => name.trim() == 'vaultwarden')) {
          return true;
        }
      }

      // Fallback: try docker-compose method
      final result = await _shell.run('docker-compose -f "$dockerComposePath" ps -q vaultwarden');

      if (result.isNotEmpty && result.first.stdout.toString().trim().isNotEmpty) {
        final containerId = result.first.stdout.toString().trim();
        final statusResult = await _shell.run('docker inspect --format="{{.State.Running}}" $containerId');
        return statusResult.first.stdout.toString().trim() == 'true';
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Start the Vaultwarden container
  Future<void> startContainer() async {
    await _shell.run('docker-compose -f "$dockerComposePath" up -d');
  }

  /// Stop the Vaultwarden container
  Future<void> stopContainer() async {
    try {
      // First try docker-compose down
      await _shell.run('docker-compose -f "$dockerComposePath" down');
    } catch (e) {
      // If docker-compose fails, try stopping by container name directly
      try {
        await _shell.run('docker stop vaultwarden');
        await _shell.run('docker rm vaultwarden');
      } catch (e2) {
        // If both methods fail, rethrow the original error
        rethrow;
      }
    }
  }

  /// Restart the container (useful after configuration changes)
  Future<void> restartContainer() async {
    try {
      await stopContainer();
      // Small delay to ensure container is fully stopped
      await Future.delayed(const Duration(seconds: 2));
      await startContainer();
    } catch (e) {
      rethrow;
    }
  }

  /// Get container logs
  Future<String> getLogs({int lines = 100}) async {
    try {
      // First try docker-compose logs
      final result = await _shell.run('docker-compose -f "$dockerComposePath" logs --tail $lines vaultwarden');
      return result.first.stdout.toString();
    } catch (e) {
      // If docker-compose fails, try getting logs by container name directly
      try {
        final result = await _shell.run('docker logs --tail $lines vaultwarden');
        return result.first.stdout.toString();
      } catch (e2) {
        return 'Error retrieving logs: $e\nFallback error: $e2';
      }
    }
  }

  /// Get detailed container status information
  Future<Map<String, dynamic>> getContainerStatus() async {
    try {
      // Check if container exists (running or stopped)
      final existsResult = await _shell.run('docker ps -a --filter "name=vaultwarden" --format "{{.Names}},{{.Status}},{{.Ports}}"');

      if (existsResult.isNotEmpty && existsResult.first.stdout.toString().trim().isNotEmpty) {
        final lines = existsResult.first.stdout.toString().trim().split('\n');
        for (final line in lines) {
          final parts = line.split(',');
          if (parts.isNotEmpty && parts[0].trim() == 'vaultwarden') {
            final status = parts.length > 1 ? parts[1] : 'Unknown';
            final ports = parts.length > 2 ? parts[2] : '';

            return {
              'exists': true,
              'running': status.toLowerCase().contains('up'),
              'status': status,
              'ports': ports,
              'name': 'vaultwarden'
            };
          }
        }
      }

      return {
        'exists': false,
        'running': false,
        'status': 'Not found',
        'ports': '',
        'name': 'vaultwarden'
      };
    } catch (e) {
      return {
        'exists': false,
        'running': false,
        'status': 'Error: $e',
        'ports': '',
        'name': 'vaultwarden'
      };
    }
  }

  /// Check if Docker is installed and running
  Future<bool> isDockerAvailable() async {
    try {
      await _shell.run('docker --version');
      await _shell.run('docker-compose --version');
      return true;
    } catch (e) {
      return false;
    }
  }
}
